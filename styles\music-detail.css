/* Music Detail Page Styles */
.music-detail-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  color: var(--text-color);
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--hover-light);
}

/* Custom scrollbar styles for Webkit browsers (Chrome, Safari, Edge) */
.music-detail-container::-webkit-scrollbar {
  width: 6px;
}

.music-detail-container::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 3px;
}

.music-detail-container::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.music-detail-container::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

.music-detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.back-button {
  margin-right: 20px;
}

.back-button a {
  color: var(--primary-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  font-size: 16px;
  transition: var(--transition);
  padding: 8px 12px;
  border-radius: var(--border-radius-small);
}

.back-button a:hover {
  color: var(--text-color);
  background-color: var(--hover-light);
}

.back-button i {
  font-size: 20px;
  margin-right: 5px;
}

.music-detail-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
}

.music-detail-content {
  display: flex;
  gap: 40px;
  margin-bottom: 40px;
}

.music-cover {
  position: relative;
  min-width: 300px;
  height: 300px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.music-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.music-cover:hover img {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: var(--transition);
}

.music-cover:hover .play-button {
  opacity: 1;
}

.play-button i {
  font-size: 60px;
  color: var(--primary-color);
  cursor: pointer;
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.5));
  transition: var(--transition);
}

.play-button i:hover {
  transform: scale(1.1);
}

.music-info {
  flex: 1;
}

.music-info h1 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.music-info h3 {
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 25px;
  color: var(--subtitle-color);
}

.music-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

/* Heart button specific styling */
.music-actions .heart-btn {
  background: linear-gradient(135deg, var(--primary-color), #2bc4cd);
  border: none;
  transition: all 0.3s ease;
}

.music-actions .heart-btn:hover {
  background: linear-gradient(135deg, #2bc4cd, var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(54, 226, 236, 0.3);
}

.music-actions .heart-btn.in-library {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.music-actions .heart-btn.in-library:hover {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
}

.music-actions .heart-btn i {
  margin-right: 8px;
  transition: all 0.3s ease;
}

.music-actions .heart-btn:hover i {
  transform: scale(1.1);
}

.music-description,
.music-lyrics {
  margin-bottom: 25px;
}

.music-description h4,
.music-lyrics h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.music-description p,
.music-lyrics p {
  color: var(--subtitle-color);
  line-height: 1.6;
}

.music-description p,
.music-lyrics p {
  font-size: 14px;
  line-height: 1.6;
  color: #b3b3b3;
}

.related-songs {
  margin-top: 40px;
}

.related-songs h3 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #fff;
}

.related-songs-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.related-song-item {
  background-color: rgba(105, 105, 170, 0.1);
  border-radius: 10px;
  padding: 15px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.related-song-item:hover {
  background-color: rgba(105, 105, 170, 0.3);
  transform: translateY(-5px);
}

.related-song-item img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 5px;
  margin-bottom: 10px;
}

.related-song-item h5 {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 5px;
}

.related-song-item .subtitle {
  font-size: 12px;
  color: #b3b3b3;
}

/* Responsive styles */
@media (max-width: 992px) {
  .music-detail-content {
    flex-direction: column;
  }

  .music-cover {
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .related-songs-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 576px) {
  .music-actions {
    flex-direction: column;
    gap: 10px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
