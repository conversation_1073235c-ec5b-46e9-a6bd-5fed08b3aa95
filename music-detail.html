<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chi tiết bài hát - Music App</title>
    <link rel="stylesheet" href="./styles/common.css" />
    <link rel="stylesheet" href="./styles/index.css" />
    <link rel="stylesheet" href="./styles/music-detail.css" />
    <link rel="stylesheet" href="./styles/notification.css" />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />
  </head>
  <body>
    <header>
      <div class="menu_side">
        <h1>Music App</h1>
        <div class="playlist">
          <h4 class="active">
            <span></span><i class="bi bi-music-note-beamed"></i> Khám Phá
          </h4>
          <h4>
            <a href="library.html" class="active">
              <span></span><i class="bi bi-music-note-beamed"></i> Thư Viện Của
              Tôi</a
            >
          </h4>
          <h4>
            <a href="recently-played.html" class="active">
              <span></span><i class="bi bi-music-note-beamed"></i> Gần Đây</a
            >
          </h4>
        </div>
        <div class="auth-links">
          <h4>
            <a href="login.html"
              ><i class="bi bi-box-arrow-in-right"></i> Đăng Nhập</a
            >
          </h4>
          <h4>
            <a href="register.html"
              ><i class="bi bi-person-plus"></i> Đăng Ký</a
            >
          </h4>
        </div>
        <div class="menu_song">
          <!-- Song list will be populated by JavaScript -->
        </div>
      </div>
      <div class="song_side">
        <nav>
          <ul>
            <li><a href="index.html">Khám Phá</a></li>
            <li><a href="library.html">Thư Viện Của Tôi</a></li>
            <li><a href="recently-played.html">Gần Đây</a></li>
            <li>Radio</li>
          </ul>
          <div class="search">
            <i class="bi bi-search"></i>
            <input type="text" placeholder="Search Music..." />
            <div class="search_result">
              <!-- Search results will be populated by JavaScript -->
            </div>
          </div>
          <div class="user">
            <img
              src="./styles/images/avatar-trang-4.jpg"
              alt="User"
              title="Guest"
            />
          </div>
        </nav>

        <div class="music-detail-container">
          <div class="music-detail-header">
            <div class="back-button">
              <a href="index.html"><i class="bi bi-arrow-left"></i> Quay lại</a>
            </div>
            <h2>Chi tiết bài hát</h2>
          </div>

          <div class="music-detail-content">
            <div class="music-cover">
              <img
                id="detail-poster"
                src="./styles/images/img/1.jpg"
                alt="Music Cover"
              />
              <div class="play-button">
                <i class="bi bi-play-circle-fill" id="detail-play-btn"></i>
              </div>
            </div>

            <div class="music-info">
              <h1 id="detail-title">Tên bài hát</h1>
              <h3 id="detail-artist">Nghệ sĩ</h3>

              <div class="music-actions">
                <button class="action-btn" id="play-btn">
                  <i class="bi bi-play-fill"></i> Phát
                </button>
                <button class="action-btn heart-btn" id="detail-heart-btn">
                  <i class="bi bi-heart" id="detail-heart-icon"></i> Yêu thích
                </button>
                <button class="action-btn" id="download-btn">
                  <i class="bi bi-cloud-arrow-down-fill"></i> Tải xuống
                </button>
              </div>

              <div class="music-description">
                <h4>Thông tin bài hát</h4>
                <p id="detail-description">
                  Thông tin chi tiết về bài hát sẽ được hiển thị ở đây. Bao gồm
                  thông tin về album, năm phát hành, và các thông tin khác.
                </p>
              </div>

              <div class="music-lyrics">
                <h4>Lời bài hát</h4>
                <p id="detail-lyrics">
                  Lời bài hát sẽ được hiển thị ở đây nếu có.
                </p>
              </div>
            </div>
          </div>

          <div class="related-songs">
            <h3>Bài hát liên quan</h3>
            <div class="related-songs-container">
              <!-- Related songs will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>

      <div class="master_play">
        <div class="wave">
          <div class="wave1"></div>
          <div class="wave1"></div>
          <div class="wave1"></div>
        </div>
        <img
          id="poster_master_play"
          src="./styles/images/img/1.jpg"
          alt="Alan"
        />
        <h5 id="title">
          On My Way<br />
          <div class="subtitle">Alan Walker</div>
        </h5>
        <div class="icon">
          <i class="bi shuffle bi-music-note-beamed">next</i>
          <i class="bi bi-skip-start-fill" id="back"></i>
          <i class="bi bi-play-fill" id="masterPlay"></i>
          <i class="bi bi-skip-end-fill" id="next"></i>
          <i
            class="bi bi-heart master-heart"
            id="masterHeart"
            title="Add to Library"
          ></i>
          <a href="" download id="download_music"
            ><i class="bi bi-cloud-arrow-down-fill"></i
          ></a>
        </div>
        <span id="currentStart">0:00</span>
        <div class="bar">
          <input type="range" id="seek" min="0" value="0" max="100" />
          <div class="bar2" id="bar2"></div>
          <div class="dot"></div>
        </div>
        <span id="currentEnd">0:00</span>

        <div class="vol">
          <i class="bi bi-volume-down-fill" id="vol_icon"></i>
          <input type="range" id="vol" min="0" value="30" max="100" />
          <div class="vol_bar"></div>
          <div class="dot" id="vol_dot"></div>
        </div>
      </div>
    </header>

    <script src="./scripts/auth.js"></script>
    <script src="./scripts/notification.js"></script>
    <script src="./scripts/master-player-context.js"></script>
    <script src="./scripts/index.js"></script>
    <script src="./scripts/music-detail.js"></script>
    <script src="./scripts/search.js"></script>
  </body>
</html>
