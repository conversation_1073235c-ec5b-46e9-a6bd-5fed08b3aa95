@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import "./common.css";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: var(--font-family);
}

body {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--background-color) 0%,
    var(--content-bg) 25%,
    var(--sidebar-color) 50%,
    var(--content-bg) 75%,
    var(--background-color) 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  position: relative;
  overflow: hidden;
}

/* Animated gradient background */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Musical note decorative elements */
body::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(54, 226, 236, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(105, 105, 170, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(54, 226, 236, 0.05) 0%,
      transparent 50%
    );
  z-index: -2;
}

/* Floating musical elements */
body::after {
  content: "♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  font-size: 20px;
  color: rgba(54, 226, 236, 0.1);
  z-index: -1;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: space-around;
  align-content: space-around;
  animation: floatNotes 20s linear infinite;
  pointer-events: none;
}

@keyframes floatNotes {
  0% {
    transform: translateY(100vh) rotate(0deg);
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
  }
}

/* Additional wave effect for music theme */
.form-wrapper::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(54, 226, 236, 0.1) 50%,
    transparent 70%
  );
  border-radius: var(--border-radius-large);
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.7;
  }
}

nav {
  position: fixed;
  padding: 25px 60px;
  z-index: 1;
}

nav a img {
  width: 167px;
}

.form-wrapper {
  position: relative;
  border-radius: var(--border-radius-large);
  padding: 40px;
  width: 400px;
  background: rgba(17, 23, 39, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(54, 226, 236, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: formAppear 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.form-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(54, 226, 236, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Enhanced keyframe animations */
@keyframes formAppear {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.9);
    filter: blur(10px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes logoAppear {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateY(5px) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes titleSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes inputFocusRipple {
  0% {
    box-shadow: 0 0 0 0 rgba(54, 226, 236, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(54, 226, 236, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(54, 226, 236, 0);
  }
}

@keyframes buttonPulse {
  0% {
    box-shadow: 0 4px 15px rgba(54, 226, 236, 0.3);
  }
  50% {
    box-shadow: 0 6px 25px rgba(54, 226, 236, 0.5);
  }
  100% {
    box-shadow: 0 4px 15px rgba(54, 226, 236, 0.3);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

@keyframes slideInError {
  0% {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    max-height: 50px;
  }
}

@keyframes slideOutError {
  0% {
    opacity: 1;
    transform: translateY(0);
    max-height: 50px;
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
}

@keyframes successGlow {
  0% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.5);
  }
}

.form-wrapper h2 {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 25px;
  animation: titleSlideIn 0.8s ease-out 0.3s both;
}

.form-wrapper form {
  margin: 25px 0;
}

form .form-control {
  height: 50px;
  position: relative;
  margin-bottom: 16px;
  animation: slideUp 0.6s ease-out both;
}

form .form-control:nth-child(1) {
  animation-delay: 0.4s;
}
form .form-control:nth-child(2) {
  animation-delay: 0.5s;
}
form .form-control:nth-child(3) {
  animation-delay: 0.6s;
}

.form-control input {
  height: 100%;
  width: 100%;
  background: rgba(76, 82, 98, 0.2);
  border: 1px solid rgba(76, 82, 98, 0.4);
  outline: none;
  border-radius: var(--border-radius-small);
  color: var(--text-color);
  font-size: 14px;
  padding: 0 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.form-control input:focus {
  background: rgba(76, 82, 98, 0.3);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(54, 226, 236, 0.2);
  transform: translateY(-2px) scale(1.02);
  animation: inputFocusRipple 0.6s ease-out;
}

.form-control input:focus ~ label {
  color: var(--primary-color) !important;
  text-shadow: 0 0 8px rgba(54, 226, 236, 0.3);
}

.form-control label {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  pointer-events: none;
  color: #4c5262;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: left center;
}

.form-control input:focus ~ label,
.form-control input:valid ~ label {
  top: 7px;
  font-size: 12px;
  color: #36e2ec;
}

form button {
  width: 100%;
  padding: 14px 0;
  font-size: 16px;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-hover) 100%
  );
  color: var(--sidebar-color);
  font-weight: 600;
  border-radius: var(--border-radius-small);
  border: none;
  outline: none;
  margin: 25px 0 10px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(54, 226, 236, 0.3);
  animation: slideUp 0.6s ease-out 0.7s both,
    buttonPulse 2s ease-in-out 1.5s infinite;
}

form button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

form button:hover {
  background: linear-gradient(
    135deg,
    var(--primary-hover) 0%,
    var(--primary-color) 100%
  );
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(54, 226, 236, 0.5);
  animation: none; /* Stop pulse animation on hover */
}

form button:hover::before {
  left: 100%;
}

form button:active {
  transform: translateY(-1px) scale(0.98);
  box-shadow: 0 4px 15px rgba(54, 226, 236, 0.4);
  transition: all 0.1s ease;
}

/* Button click ripple effect */
form button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

form button:active::after {
  width: 300px;
  height: 300px;
}

.form-wrapper a {
  text-decoration: none;
  color: #36e2ec;
  transition: 0.3s ease;
}

.form-wrapper a:hover {
  text-decoration: underline;
  color: #2bc4cd;
}

.form-wrapper :where(p, small) {
  color: #4c5262;
  font-size: 14px;
}

form .form-help {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  animation: slideUp 0.6s ease-out 0.75s both;
}

form .remember-me {
  display: flex;
  align-items: center;
}

form .remember-me input {
  margin-right: 5px;
  accent-color: #36e2ec;
}

form .form-help :where(label, a) {
  font-size: 13px;
  color: #4c5262;
}

.form-wrapper p {
  margin-top: 15px;
  text-align: center;
  animation: slideUp 0.6s ease-out 0.8s both;
}

.form-wrapper p a {
  color: #36e2ec;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.form-wrapper p a::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #36e2ec, #2bc5ce);
  transition: width 0.3s ease;
}

.form-wrapper p a:hover::after {
  width: 100%;
}

.form-wrapper p a:hover {
  text-shadow: 0 0 8px rgba(54, 226, 236, 0.4);
  transform: translateY(-1px);
}

.form-wrapper .logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #36e2ec;
  animation: logoAppear 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.2s both;
}

.form-wrapper .logo i {
  font-size: 28px;
  margin-right: 10px;
  transition: all 0.3s ease;
}

.form-wrapper .logo:hover i {
  transform: rotate(360deg) scale(1.1);
  text-shadow: 0 0 15px rgba(54, 226, 236, 0.6);
}

.form-wrapper .logo span {
  font-size: 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.form-wrapper .logo:hover span {
  text-shadow: 0 0 10px rgba(54, 226, 236, 0.4);
}

/* Error message animations */
.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 5px;
  padding: 8px 12px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--border-radius-small);
  animation: slideInError 0.3s ease-out;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.error-message.hide {
  animation: slideOutError 0.3s ease-out forwards;
}

/* Input error state */
.form-control.error input {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  animation: shake 0.5s ease-in-out;
}

.form-control.error label {
  color: #ef4444 !important;
}

/* Success state */
.form-control.success input {
  border-color: #22c55e;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  animation: successGlow 1s ease-in-out;
}

.form-control.success label {
  color: #22c55e !important;
}

/* Loading state for button */
.form-wrapper button.loading {
  pointer-events: none;
  opacity: 0.8;
  position: relative;
}

.form-wrapper button.loading::before {
  display: none;
}

.form-wrapper button.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--sidebar-color);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@media (max-width: 740px) {
  body {
    background-size: 200% 200%;
    animation: gradientShift 10s ease infinite;
  }

  body::after {
    font-size: 16px;
    animation: floatNotes 15s linear infinite;
  }

  nav,
  .form-wrapper {
    padding: 20px;
  }

  nav a img {
    width: 140px;
  }

  .form-wrapper {
    width: 100%;
    margin: 20px;
    padding: 30px 20px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  .form-wrapper:hover {
    transform: none;
  }
}

@media (max-width: 480px) {
  body::after {
    font-size: 14px;
    animation: floatNotes 12s linear infinite;
  }

  .form-wrapper {
    margin: 10px;
    padding: 25px 15px;
    border-radius: var(--border-radius);
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  body {
    animation: none;
    background: var(--content-bg);
  }

  body::after {
    animation: none;
  }

  .form-wrapper,
  .form-wrapper .logo,
  .form-wrapper h2,
  form .form-control,
  form button {
    animation: none;
  }

  form button::before,
  form button::after {
    display: none;
  }

  .form-control input:focus {
    animation: none;
  }

  .form-control.error input {
    animation: none;
  }

  .form-control.success input {
    animation: none;
  }

  .error-message {
    animation: none;
  }
}
