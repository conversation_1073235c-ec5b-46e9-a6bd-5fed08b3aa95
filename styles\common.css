/* Common CSS styles for consistent styling across all pages */

/* Common variables */
:root {
  --primary-color: #36e2ec;
  --primary-hover: #2bc5ce;
  --secondary-color: rgba(105, 105, 170, 0.7);
  --secondary-hover: rgba(105, 105, 170, 0.9);
  --background-color: #131312;
  --sidebar-color: #111727;
  --content-bg: #0b1320;
  --text-color: #fff;
  --text-muted: #4c5262;
  --subtitle-color: #b3b3b3;
  --hover-color: rgba(105, 105, 170, 0.3);
  --hover-light: rgba(105, 105, 170, 0.1);
  --active-color: rgba(105, 105, 170, 0.7);
  --border-color: rgba(76, 82, 98, 0.2);
  --border-radius: 10px;
  --border-radius-small: 5px;
  --border-radius-large: 15px;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  --box-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.2);
  --transition: all 0.3s ease;
  --transition-fast: all 0.2s ease;
  --font-family: "Poppins", sans-serif;
  --z-index-modal: 10000;
  --z-index-notification: 9999;
  --z-index-master-player: 999;
  --z-index-search: 1000;
}

/* Common animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Common button styles */
.action-btn {
  background-color: var(--secondary-color);
  border: none;
  color: var(--text-color);
  padding: 10px 20px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  text-decoration: none;
  min-height: 40px;
  white-space: nowrap;
}

.action-btn:hover {
  background-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: var(--box-shadow-light);
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.in-library {
  background-color: var(--primary-color);
}

.action-btn.primary {
  background-color: var(--primary-color);
  color: var(--background-color);
}

.action-btn.primary:hover {
  background-color: var(--primary-hover);
}

.action-btn.secondary {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.action-btn.secondary:hover {
  background-color: var(--hover-light);
  border-color: var(--primary-color);
}

.action-btn i {
  margin-right: 8px;
  font-size: 16px;
}

.action-btn:last-child i,
.action-btn.icon-only i {
  margin-right: 0;
}

/* Common song item styles */
.songItem {
  position: relative;
  transition: var(--transition);
}

.songItem:hover {
  background-color: var(--hover-color) !important;
}

.songItem h5 {
  color: var(--text-color);
}

.songItem .subtitle {
  color: var(--subtitle-color);
  font-size: smaller;
}

/* Common song actions */
.song-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.songItem:hover .song-actions {
  opacity: 1;
}

.add-to-library {
  cursor: pointer;
  color: var(--text-color);
  font-size: 18px;
  transition: var(--transition);
}

.add-to-library:hover {
  color: var(--primary-color);
  transform: scale(1.1);
}

/* Common navigation styles */
.nav-link {
  color: var(--text-muted);
  text-decoration: none;
  transition: var(--transition);
  display: flex;
  align-items: center;
}

.nav-link:hover {
  color: var(--text-color);
}

.nav-link.active {
  color: var(--primary-color);
}

.nav-link i {
  margin-right: 8px;
  font-size: 16px;
}

/* Common input styles */
.form-input {
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  color: var(--text-color);
  padding: 10px 15px;
  font-size: 14px;
  transition: var(--transition);
  width: 100%;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(54, 226, 236, 0.2);
}

.form-input::placeholder {
  color: var(--text-muted);
}

/* Fix for menu_side and song_side alignment */
.menu_side,
.song_side {
  height: 100%;
  overflow-y: auto;
}

/* Fix for master_play positioning */
.master_play {
  position: fixed;
  bottom: 0;
  width: 85%;
  z-index: var(--z-index-master-player);
  background-color: var(--sidebar-color);
  box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);
}

/* Global scrollbar styles for consistent theming */
.custom-scrollbar {
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--hover-light);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

/* Horizontal scrollbar variant */
.custom-scrollbar-horizontal {
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--hover-light);
}

.custom-scrollbar-horizontal::-webkit-scrollbar {
  height: 5px;
}

.custom-scrollbar-horizontal::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 10px;
}

.custom-scrollbar-horizontal::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.custom-scrollbar-horizontal::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

/* Fix for search results */
.search_result {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--sidebar-color);
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
  z-index: var(--z-index-search);
  border: 1px solid var(--border-color);
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--hover-light);
}

.search_result::-webkit-scrollbar {
  width: 6px;
}

.search_result::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 3px;
}

.search_result::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.search_result::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

/* Common card styles */
.card {
  background-color: var(--hover-light);
  border-radius: var(--border-radius);
  padding: 15px;
  transition: var(--transition);
  cursor: pointer;
  border: 1px solid transparent;
}

.card:hover {
  background-color: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-light);
  border-color: var(--border-color);
}

.card.active {
  border-color: var(--primary-color);
  background-color: var(--hover-color);
}

/* Common loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Common text utilities */
.text-primary {
  color: var(--primary-color);
}

.text-muted {
  color: var(--text-muted);
}

.text-subtitle {
  color: var(--subtitle-color);
}

.text-center {
  text-align: center;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Mobile scrollbar adjustments */
@media (max-width: 768px) {
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar-horizontal::-webkit-scrollbar {
    height: 4px;
  }

  /* Make scrollbars more visible on mobile */
  .library-container::-webkit-scrollbar,
  .recently-played-container::-webkit-scrollbar,
  .music-detail-container::-webkit-scrollbar {
    width: 4px;
  }

  .search_result::-webkit-scrollbar,
  header .song_side nav .search .search_result::-webkit-scrollbar {
    width: 4px;
  }
}

/* Fix for responsive layouts */
@media (max-width: 992px) {
  header {
    width: 95%;
  }

  .master_play {
    width: 95%;
  }

  .action-btn {
    padding: 8px 16px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .menu_side {
    width: 100%;
    z-index: 999;
  }

  .song_side {
    width: 100%;
    z-index: 1;
  }

  .master_play {
    width: 100%;
  }

  .action-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .menu_side h1 {
    font-size: 18px;
  }

  .master_play h5 {
    font-size: 11px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-height: 36px;
  }

  .card {
    padding: 12px;
  }
}
