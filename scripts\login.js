// Lấy form đăng nhập
const loginForm = document.getElementById("login-form");

// L<PERSON>y các trường input
const emailInput = document.getElementById("email");
const passwordInput = document.getElementById("password");
const rememberMeCheckbox = document.getElementById("remember-me");

// Animation and validation utilities
const showError = (input, message) => {
  const formControl = input.parentElement;
  const existingError = formControl.querySelector(".error-message");

  // Remove existing error message
  if (existingError) {
    existingError.classList.add("hide");
    setTimeout(() => existingError.remove(), 300);
  }

  // Add error class with animation
  formControl.classList.remove("success");
  formControl.classList.add("error");

  // Create and show new error message
  const errorDiv = document.createElement("div");
  errorDiv.className = "error-message";
  errorDiv.textContent = message;
  formControl.appendChild(errorDiv);

  // Remove error state after 3 seconds
  setTimeout(() => {
    formControl.classList.remove("error");
    if (errorDiv.parentElement) {
      errorDiv.classList.add("hide");
      setTimeout(() => errorDiv.remove(), 300);
    }
  }, 3000);
};

const showSuccess = (input) => {
  const formControl = input.parentElement;
  const existingError = formControl.querySelector(".error-message");

  // Remove existing error message
  if (existingError) {
    existingError.classList.add("hide");
    setTimeout(() => existingError.remove(), 300);
  }

  // Add success class
  formControl.classList.remove("error");
  formControl.classList.add("success");

  // Remove success state after 2 seconds
  setTimeout(() => {
    formControl.classList.remove("success");
  }, 2000);
};

const setLoadingState = (button, isLoading) => {
  if (isLoading) {
    button.classList.add("loading");
    button.disabled = true;
    button.textContent = "Đang đăng nhập...";
  } else {
    button.classList.remove("loading");
    button.disabled = false;
    button.textContent = "Sign In";
  }
};

// Kiểm tra xem có thông tin đăng nhập được lưu không
const checkSavedCredentials = () => {
  const savedEmail = localStorage.getItem("rememberedEmail");
  const savedPassword = localStorage.getItem("rememberedPassword");

  if (savedEmail && savedPassword) {
    emailInput.value = savedEmail;
    passwordInput.value = savedPassword;
    rememberMeCheckbox.checked = true;
  }
};

// Lưu thông tin đăng nhập nếu người dùng chọn "Remember me"
const saveCredentials = (email, password) => {
  if (rememberMeCheckbox.checked) {
    localStorage.setItem("rememberedEmail", email);
    localStorage.setItem("rememberedPassword", password);
  } else {
    localStorage.removeItem("rememberedEmail");
    localStorage.removeItem("rememberedPassword");
  }
};

// Xử lý đăng nhập
const handleLogin = (email, password) => {
  // Lấy danh sách người dùng từ localStorage
  const users = JSON.parse(localStorage.getItem("users")) || [];

  // Tìm người dùng với email tương ứng
  const user = users.find((u) => u.email === email);

  if (user && user.password === password) {
    // Lưu trạng thái đăng nhập
    localStorage.setItem("currentUser", JSON.stringify(user));

    // Cập nhật giao diện nếu đang ở trang chính
    if (typeof checkAuthState === "function") {
      checkAuthState();
    }

    // Chuyển hướng về trang chủ
    window.location.href = "index.html";
    return true;
  }
  return false;
};

// Xử lý sự kiện submit form
loginForm.addEventListener("submit", async (e) => {
  e.preventDefault();

  const email = emailInput.value.trim();
  const password = passwordInput.value;
  const submitButton = loginForm.querySelector('button[type="submit"]');

  // Reset previous states
  document.querySelectorAll(".form-control").forEach((control) => {
    control.classList.remove("error", "success");
  });

  // Validation with animations
  let hasError = false;

  if (!email) {
    showError(emailInput, "Vui lòng nhập email!");
    hasError = true;
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    showError(emailInput, "Email không hợp lệ!");
    hasError = true;
  } else {
    showSuccess(emailInput);
  }

  if (!password) {
    showError(passwordInput, "Vui lòng nhập mật khẩu!");
    hasError = true;
  } else if (password.length < 6) {
    showError(passwordInput, "Mật khẩu phải có ít nhất 6 ký tự!");
    hasError = true;
  } else {
    showSuccess(passwordInput);
  }

  if (hasError) return;

  // Show loading state
  setLoadingState(submitButton, true);

  // Simulate API call delay for better UX
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Thực hiện đăng nhập
  if (handleLogin(email, password)) {
    // Lưu thông tin đăng nhập nếu được chọn
    saveCredentials(email, password);

    // Show success message briefly before redirect
    submitButton.textContent = "Đăng nhập thành công!";
    submitButton.style.background =
      "linear-gradient(135deg, #22c55e 0%, #16a34a 100%)";

    setTimeout(() => {
      window.location.href = "index.html";
    }, 500);
  } else {
    setLoadingState(submitButton, false);
    showError(passwordInput, "Email hoặc mật khẩu không chính xác!");
  }
});

// Real-time validation feedback
emailInput.addEventListener("blur", () => {
  const email = emailInput.value.trim();
  if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    showError(emailInput, "Email không hợp lệ!");
  } else if (email) {
    showSuccess(emailInput);
  }
});

passwordInput.addEventListener("blur", () => {
  const password = passwordInput.value;
  if (password && password.length < 6) {
    showError(passwordInput, "Mật khẩu phải có ít nhất 6 ký tự!");
  } else if (password) {
    showSuccess(passwordInput);
  }
});

// Clear error states on input focus
[emailInput, passwordInput].forEach((input) => {
  input.addEventListener("focus", () => {
    const formControl = input.parentElement;
    const existingError = formControl.querySelector(".error-message");
    if (existingError) {
      existingError.classList.add("hide");
      setTimeout(() => existingError.remove(), 300);
    }
    formControl.classList.remove("error");
  });
});

// Kiểm tra thông tin đăng nhập đã lưu khi trang được tải
document.addEventListener("DOMContentLoaded", checkSavedCredentials);
