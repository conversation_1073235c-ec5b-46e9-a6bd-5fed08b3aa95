// Lấy form đăng ký
const registerForm = document.getElementById("register-form");

// L<PERSON><PERSON> các trường input
const emailInput = document.getElementById("email");
const passwordInput = document.getElementById("password");
const confirmPasswordInput = document.getElementById("confirm-password");

// Animation and validation utilities
const showError = (input, message) => {
  const formControl = input.parentElement;
  const existingError = formControl.querySelector(".error-message");

  // Remove existing error message
  if (existingError) {
    existingError.classList.add("hide");
    setTimeout(() => existingError.remove(), 300);
  }

  // Add error class with animation
  formControl.classList.remove("success");
  formControl.classList.add("error");

  // Create and show new error message
  const errorDiv = document.createElement("div");
  errorDiv.className = "error-message";
  errorDiv.textContent = message;
  formControl.appendChild(errorDiv);

  // Remove error state after 3 seconds
  setTimeout(() => {
    formControl.classList.remove("error");
    if (errorDiv.parentElement) {
      errorDiv.classList.add("hide");
      setTimeout(() => errorDiv.remove(), 300);
    }
  }, 3000);
};

const showSuccess = (input) => {
  const formControl = input.parentElement;
  const existingError = formControl.querySelector(".error-message");

  // Remove existing error message
  if (existingError) {
    existingError.classList.add("hide");
    setTimeout(() => existingError.remove(), 300);
  }

  // Add success class
  formControl.classList.remove("error");
  formControl.classList.add("success");

  // Remove success state after 2 seconds
  setTimeout(() => {
    formControl.classList.remove("success");
  }, 2000);
};

const setLoadingState = (button, isLoading) => {
  if (isLoading) {
    button.classList.add("loading");
    button.disabled = true;
    button.textContent = "Đang đăng ký...";
  } else {
    button.classList.remove("loading");
    button.disabled = false;
    button.textContent = "Sign Up";
  }
};

// Kiểm tra email có hợp lệ không
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Kiểm tra mật khẩu có đủ mạnh không
const isValidPassword = (password) => {
  // Mật khẩu phải có ít nhất 6 ký tự
  return password.length >= 6;
};

// Kiểm tra email đã tồn tại chưa
const isEmailExists = (email) => {
  const users = JSON.parse(localStorage.getItem("users")) || [];
  return users.some((user) => user.email === email);
};

// Lưu người dùng mới vào localStorage
const saveUser = (email, password) => {
  const users = JSON.parse(localStorage.getItem("users")) || [];

  // Tạo người dùng mới
  const newUser = {
    id: Date.now(), // Tạo ID duy nhất
    email: email,
    password: password,
    createdAt: new Date().toISOString(),
  };

  // Thêm vào danh sách người dùng
  users.push(newUser);

  // Lưu lại vào localStorage
  localStorage.setItem("users", JSON.stringify(users));

  return newUser;
};

// Xử lý sự kiện submit form
registerForm.addEventListener("submit", async (e) => {
  e.preventDefault();

  const email = emailInput.value.trim();
  const password = passwordInput.value;
  const confirmPassword = confirmPasswordInput.value;
  const submitButton = registerForm.querySelector('button[type="submit"]');

  // Reset previous states
  document.querySelectorAll(".form-control").forEach((control) => {
    control.classList.remove("error", "success");
  });

  // Validation with animations
  let hasError = false;

  // Email validation
  if (!email) {
    showError(emailInput, "Vui lòng nhập email!");
    hasError = true;
  } else if (!isValidEmail(email)) {
    showError(emailInput, "Email không hợp lệ!");
    hasError = true;
  } else if (isEmailExists(email)) {
    showError(emailInput, "Email đã được sử dụng!");
    hasError = true;
  } else {
    showSuccess(emailInput);
  }

  // Password validation
  if (!password) {
    showError(passwordInput, "Vui lòng nhập mật khẩu!");
    hasError = true;
  } else if (!isValidPassword(password)) {
    showError(passwordInput, "Mật khẩu phải có ít nhất 6 ký tự!");
    hasError = true;
  } else {
    showSuccess(passwordInput);
  }

  // Confirm password validation
  if (!confirmPassword) {
    showError(confirmPasswordInput, "Vui lòng xác nhận mật khẩu!");
    hasError = true;
  } else if (password !== confirmPassword) {
    showError(confirmPasswordInput, "Mật khẩu xác nhận không khớp!");
    hasError = true;
  } else if (password && confirmPassword) {
    showSuccess(confirmPasswordInput);
  }

  if (hasError) return;

  // Show loading state
  setLoadingState(submitButton, true);

  // Simulate API call delay for better UX
  await new Promise((resolve) => setTimeout(resolve, 1500));

  try {
    // Lưu người dùng mới
    const newUser = saveUser(email, password);

    // Tự động đăng nhập sau khi đăng ký
    localStorage.setItem("currentUser", JSON.stringify(newUser));

    // Show success message
    submitButton.textContent = "Đăng ký thành công!";
    submitButton.style.background =
      "linear-gradient(135deg, #22c55e 0%, #16a34a 100%)";

    // Show success animation on all inputs
    [emailInput, passwordInput, confirmPasswordInput].forEach((input) => {
      showSuccess(input);
    });

    // Redirect after success animation
    setTimeout(() => {
      window.location.href = "login.html";
    }, 1000);
  } catch (error) {
    setLoadingState(submitButton, false);
    showError(emailInput, "Có lỗi xảy ra, vui lòng thử lại!");
  }
});

// Real-time validation feedback
emailInput.addEventListener("blur", () => {
  const email = emailInput.value.trim();
  if (email && !isValidEmail(email)) {
    showError(emailInput, "Email không hợp lệ!");
  } else if (email && isEmailExists(email)) {
    showError(emailInput, "Email đã được sử dụng!");
  } else if (email) {
    showSuccess(emailInput);
  }
});

passwordInput.addEventListener("blur", () => {
  const password = passwordInput.value;
  if (password && !isValidPassword(password)) {
    showError(passwordInput, "Mật khẩu phải có ít nhất 6 ký tự!");
  } else if (password) {
    showSuccess(passwordInput);
  }
});

confirmPasswordInput.addEventListener("blur", () => {
  const password = passwordInput.value;
  const confirmPassword = confirmPasswordInput.value;
  if (confirmPassword && password !== confirmPassword) {
    showError(confirmPasswordInput, "Mật khẩu xác nhận không khớp!");
  } else if (confirmPassword && password) {
    showSuccess(confirmPasswordInput);
  }
});

// Clear error states on input focus
[emailInput, passwordInput, confirmPasswordInput].forEach((input) => {
  input.addEventListener("focus", () => {
    const formControl = input.parentElement;
    const existingError = formControl.querySelector(".error-message");
    if (existingError) {
      existingError.classList.add("hide");
      setTimeout(() => existingError.remove(), 300);
    }
    formControl.classList.remove("error");
  });
});
